services:
  raycast2api:
    image: ghcr.io/missuo/raycast2api:latest
    container_name: raycast2api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      # Raycast API Key
      - RAYCAST_BEARER_TOKEN=XXXX
      # YOUR API Key
      - API_KEY=XXXX
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    volumes:
      - ./logs:/app/logs